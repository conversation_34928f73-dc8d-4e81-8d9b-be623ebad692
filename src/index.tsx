#!/usr/bin/env node

import chalk from 'chalk';

// Handle errors gracefully
process.on('uncaughtException', (error: Error): void => {
  console.error('Error:', error);
  process.exit(1);
});

interface BoxOptions {
  label: string;
  content: string;
  color: (text: string) => string;
}

// Create boxes with proper TypeScript typing
const createBox = ({ label, content, color }: BoxOptions): string => {
  const width: number = Math.min(process.stdout.columns - 4, 80);
  const top: string = `┌─ ${label} ${'─'.repeat(width - label.length - 5)}┐`;
  const lines: string[] = content.split('\n').map((line: string): string => {
    const padding: string = ' '.repeat(Math.max(0, width - line.length - 3));
    return `│ ${line}${padding}│`;
  });
  const bottom: string = `└${'─'.repeat(width - 2)}┘`;
  return color(top) + '\n' + lines.join('\n') + '\n' + color(bottom);
};

const inputBox = (userInput: string = ''): string => {
  const prompt: string = '> ';
  const placeholder: string = userInput || 'Type your message or @path/to/file';
  const content: string = prompt + placeholder;
  return createBox({ label: 'Input', content, color: chalk.cyan });
};

const outputBox = (text: string): string => {
  return createBox({ label: 'Output', content: text, color: chalk.green });
};

// Main CLI with proper TypeScript
let currentInput: string = '';

console.log(chalk.bold.cyan('\n🤖 AgentZero CLI'));
console.log(chalk.dim('Press Ctrl+C to exit\n'));

// Custom input handling to show input inside the box
process.stdin.setRawMode(true);
process.stdin.setEncoding('utf8');

const redrawInput = (): void => {
  // Clear current line and move cursor up to overwrite the input box
  process.stdout.write('\x1b[2K\r'); // Clear line
  process.stdout.write('\x1b[4A'); // Move up 4 lines (height of input box)
  console.log(inputBox(currentInput));
};

const processCommand = (cmd: string): void => {
  if (cmd === 'exit' || cmd === 'quit') {
    process.stdout.write('\x1b[?25h'); // Show cursor
    console.log(chalk.dim('\n👋 Goodbye!'));
    process.exit(0);
  }

  if (cmd === 'clear') {
    console.clear();
    console.log(chalk.bold.cyan('\n🤖 AgentZero CLI'));
    console.log(chalk.dim('Press Ctrl+C to exit\n'));
    currentInput = '';
    console.log(inputBox());
    return;
  }

  // Show output and reset input
  console.log('\n' + outputBox(`Echo: ${cmd}`));
  currentInput = '';
  console.log('\n' + inputBox());
};

// Initial display
console.log(inputBox());

process.stdin.on('data', (key: string): void => {
  const keyCode: number = key.charCodeAt(0);

  if (keyCode === 3) { // Ctrl+C
    process.stdout.write('\x1b[?25h'); // Show cursor
    console.log(chalk.dim('\n👋 Goodbye!'));
    process.exit(0);
  }

  if (keyCode === 13) { // Enter
    const cmd: string = currentInput.trim();
    if (cmd) {
      processCommand(cmd);
    } else {
      redrawInput();
    }
    return;
  }

  if (keyCode === 127) { // Backspace
    if (currentInput.length > 0) {
      currentInput = currentInput.slice(0, -1);
      redrawInput();
    }
    return;
  }

  // Regular character input
  if (keyCode >= 32 && keyCode <= 126) {
    currentInput += key;
    redrawInput();
  }
});

process.on('SIGINT', (): void => {
  process.stdout.write('\x1b[?25h'); // Show cursor
  console.log(chalk.dim('\n👋 Goodbye!'));
  process.exit(0);
});
